import { FileChangeType, FilePermission, FileStore } from '@/utils/graphEditor/data';
import {
  DeleteIcon,
  FileIcon,
  FolderIcon,
  ReadOnlyFileIcon,
  RenameIcon,
  ShareIcon,
  SharedFolderIcon,
  TutorialIcon,
} from '@/pages/home/<USER>';
import { TreeLabelInteractable } from 'baseui/tree-view';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useMutation } from 'react-query';
import { MouseEvent, useEffect, useState, useRef } from 'react';

import { renameFileReq } from '@/pages/editor/file/api';
import { useSaveFileMutation } from '@/pages/editor/file/hooks';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { showToast } from '@/components/styledToasterContainer';

import { FileAction, FileActionsDropdown } from '@/pages/editor/file/FileActionsDropdown';
import { useTheme } from '@/contexts/themeContext';
import { MdAdd } from 'react-icons/md';
import { Button } from '@tigergraph/app-ui-lib/button';

import { getErrorMessage } from '@/utils/utils';
import { AxiosError } from 'axios';
import { FaInfoCircle, FaSave } from 'react-icons/fa';
import { draggable, dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import clsx from 'clsx';
import { FileNameDisplay } from '@/pages/editor/file/FileNameDisplay';

export interface FileItemProps {
  file: FileStore;
  parent: FileStore | null;
  allFiles: FileStore[];
  newFileId: string;
  setNewFileId: (fileId: string) => void;
  onSelectFile: (file: FileStore) => void;
  onChangeFile: (type: FileChangeType, payload: FileStore) => void;
  onCreateFile: (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;
  onOpenShareDrawer: (file: FileStore) => void;
  onToggleExpand: (fileId: string) => void;
  onFileMenuOpen?: (isOpen: boolean) => void;
  onDeleteFile: (file: FileStore) => void;
  onMoveFile: (fromId: string, parentFileId: string | null) => void;
}

function isChild(file: FileStore, parent: FileStore | null) {
  if (!parent) {
    return !file.parent_id;
  }

  return !!parent.files?.find((f) => f.id === file.id);
}

function isFileDroppable(target: FileStore | null, source: FileStore) {
  if (isChild(source, target)) {
    return false;
  }

  return !target || (target.is_folder && target.type === 'UserFile');
}

function isFileStore(data: any): data is FileStore {
  return 'id' in data && 'is_folder' in data;
}

export function FileItem({
  file,
  parent,
  allFiles,
  newFileId,
  setNewFileId,
  onSelectFile,
  onChangeFile,
  onCreateFile,
  onOpenShareDrawer,
  onToggleExpand,
  onFileMenuOpen,
  onDeleteFile,
  onMoveFile,
}: FileItemProps) {
  const { currentFileId, setActiveFiles, unsavedFiles } = useEditorContext();

  const [, theme] = useStyletron();
  const { themeType } = useTheme();

  const [isEditing, setIsEditing] = useState<boolean>(false);
  useEffect(() => {
    if (newFileId === file.id) {
      setIsEditing(true);
    }
  }, [newFileId, file.id]);

  const exitEditing = () => {
    setIsEditing(false);
    setNewFileId('');
  };

  const isOwner = file.permission === FilePermission.Owner;

  const userFolders = allFiles.filter((file) => file.is_folder && file.type === 'UserFile');

  const isActive = file.id === currentFileId;

  const handleClickFile = () => {
    if (file.is_folder) {
      onToggleExpand(file.id);
    } else if (file.id !== currentFileId) {
      onSelectFile(file);
    }
  };

  const renameFileClient = useMutation('renameFile', renameFileReq, {
    onSuccess: (data, variables) => {
      exitEditing();
      onChangeFile(FileChangeType.UPDATE, { id: file.id, name: variables.name } as FileStore);
    },
    onError: (error: AxiosError) => {
      showToast({ kind: 'negative', message: getErrorMessage(error) });
      exitEditing();
    },
  });

  const handleRenameFile = (newName: string) => {
    if (newName === file.name) {
      exitEditing();
      return;
    }
    if (!newName) {
      showToast({ kind: 'negative', message: 'File name cannot be empty' });
      exitEditing();
      return;
    }
    // check whether newName is duplicate with other files in the same folder
    if (allFiles.find((f) => f.name === newName && f.parent_id === file.parent_id && f.id !== file.id)) {
      showToast({ kind: 'negative', message: 'File name already exists' });
      exitEditing();
      return;
    }

    renameFileClient.mutate({
      fileId: file.id,
      name: newName,
    });
  };

  const { mutate: saveFile } = useSaveFileMutation();

  const unsaved = !file.is_folder && unsavedFiles.find((f) => f.id === file.id || f.file_id === file.id);
  const fileActions: FileAction[] = [
    {
      label: 'Save',
      handleFn: () => {
        let unsavedFile = unsavedFiles.find((f) => f.id === file.id || f.file_id === file.id)!;
        saveFile(
          { content: unsavedFile.content, fileId: unsavedFile.file_id || unsavedFile.id },
          {
            onSuccess(data) {
              const { updated_at, id, content } = data.Result!;
              onChangeFile(FileChangeType.UPDATE, {
                id,
                updated_at,
                content,
              } as FileStore);
            },
          }
        );
      },
      disabled: !unsaved,
      hidden: ![FilePermission.Edit, FilePermission.Owner].includes(file.permission),
      icon: <FaSave size={16} color={theme.colors['icon.primary']} />,
    },
    {
      label: 'Share',
      handleFn: () => onOpenShareDrawer(file),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <ShareIcon />,
    },
    {
      label: 'Rename',
      handleFn: () => setIsEditing(true),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <RenameIcon />,
    },
    {
      label: 'Delete',
      handleFn: () => onDeleteFile(file),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <DeleteIcon />,
    },
    {
      label: 'File Info',
      disabled: false,
      hidden: !parent?.isShared,
      icon: <FaInfoCircle size={16} color={theme.colors['icon.primary']} />,
    },
  ];

  const folderActions: FileAction[] = [
    {
      label: 'Rename',
      handleFn: () => setIsEditing(true),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <RenameIcon />,
    },
    {
      label: 'Delete',
      handleFn: () => onDeleteFile(file),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <DeleteIcon />,
    },
  ];

  const displayActions = (file.is_folder ? folderActions : fileActions).filter((action) => !action.hidden);

  const ref = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!file.is_folder && file.type === 'UserFile') {
      const treeItem = ref.current!.closest('[role="treeitem"]') as HTMLElement;
      if (!treeItem) {
        return;
      }

      return draggable({
        element: treeItem,
        getInitialData: () => ({ ...file }),
      });
    }
  }, [file]);

  useEffect(() => {
    function makeElemDroppable(element: HTMLElement, file: FileStore | null) {
      return dropTargetForElements({
        element,
        canDrop: ({ source }) => {
          return isFileStore(source.data);
        },
        onDragEnter: ({ self, location, source }) => {
          if (location.current.dropTargets[0]?.element !== self.element) {
            return;
          }

          const data = source.data;
          if (!isFileStore(data)) {
            return;
          }

          if (isFileDroppable(file, data)) {
            element.style.backgroundColor = theme.colors['background.accent.neutral.subtler'];
          }
          // deactive other drop target
          const target = location.current.dropTargets.find((t) => t.element !== self.element);
          if (target) {
            (target.element as HTMLElement).style.backgroundColor = '';
          }
        },
        onDragLeave: ({ self, location, source }) => {
          element.style.backgroundColor = '';

          // if there is other drop target, active it
          const target = location.current.dropTargets.find((t) => t.element !== self.element);
          if (target && target.element.getAttribute('role') === 'tree') {
            (target.element as HTMLElement).style.backgroundColor = theme.colors['background.accent.neutral.subtler'];
          }
        },
        onDrop: ({ self, location, source }) => {
          if (location.current.dropTargets[0]?.element !== self.element) {
            return;
          }

          element.style.backgroundColor = '';

          const data = source.data;
          if (!isFileStore(data)) {
            return;
          }

          if (!isFileDroppable(file, data)) {
            return;
          }

          onMoveFile(data.id, file ? file.id : null);
        },
      });
    }

    // make every user folder droppable
    if (file.is_folder) {
      const treeItem = ref.current!.closest('[role="treeitem"]') as HTMLElement;
      if (treeItem && !treeItem.getAttribute('data-drop-target-for-element')) {
        makeElemDroppable(treeItem, file);
      }
    }

    // make the outside of the tree droppable
    // this will be triggered once since there is only one Shared folder
    if (file.is_folder && file.isShared) {
      const treeRoot = ref.current!.closest('[role="tree"]') as HTMLElement;
      if (treeRoot && !treeRoot.getAttribute('data-drop-target-for-element')) {
        makeElemDroppable(treeRoot, null);
      }
    }
  }, [file, onMoveFile, theme.colors]);

  return (
    <TreeLabelInteractable>
      <div
        data-baseweb={'tree-node-label'}
        ref={ref}
        className={clsx(
          'flex items-center justify-between text-primary',
          file.is_folder ? 'cursor-default' : 'cursor-pointer'
        )}
        style={{ color: theme.colors['text.primary'] }}
        onClick={handleClickFile}
      >
        <div
          className="flex items-center"
          style={{
            flexGrow: 2,
            width: `calc(100% - ${file.is_folder ? 40 : 24}px)`,
          }}
        >
          <div className="mr-1">
            {file.is_folder ? (
              file.isShared ? (
                <SharedFolderIcon />
              ) : file.type === 'Tutorial' ? (
                <TutorialIcon />
              ) : (
                <FolderIcon />
              )
            ) : file.permission === FilePermission.View ? (
              <ReadOnlyFileIcon />
            ) : (
              <FileIcon />
            )}
          </div>
          <FileNameDisplay
            file={file}
            isEditing={isEditing}
            onRename={handleRenameFile}
            onCancelEdit={exitEditing}
            unsaved={!!unsaved}
          />
        </div>
        <div className={clsx('h-5 flex', 'file-actions')}>
          {file.is_folder && [FilePermission.Edit, FilePermission.Owner].includes(file.permission) && (
            <Button
              size="compact"
              kind="text"
              shape="square"
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event.stopPropagation();
                onCreateFile({ is_folder: false, content: '', parentId: file.id });
              }}
            >
              <MdAdd size={20} />
            </Button>
          )}
          {displayActions.length > 0 && (
            <FileActionsDropdown
              file={file}
              displayActions={displayActions}
              userFolders={userFolders}
              onFileMenuOpen={onFileMenuOpen}
              onMoveFile={onMoveFile}
            />
          )}
        </div>
      </div>
    </TreeLabelInteractable>
  );
}
