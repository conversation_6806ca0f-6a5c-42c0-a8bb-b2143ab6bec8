import { render, screen, fireEvent, findByTitle, waitFor } from '@/test-utils';
import { FileList } from '@/pages/editor/file/FileList';
import { FileChangeType, FilePermission, FileStore, FileType } from '@/utils/graphEditor/data';
import { cloneDeep } from 'lodash-es';

function getMockFile(): FileStore {
  return {
    id: '',
    name: '',
    is_folder: false,
    created_at: '',
    updated_at: '',
    permission: FilePermission.Edit,
    files: [],
    content: '',
    parent_id: '',
    type: 'UserFile' as FileType,
  };
}

const mockFiles: FileStore[] = [
  {
    ...getMockFile(),
    id: '1',
    name: 'Folder 1',
    is_folder: true,
    files: [
      {
        ...getMockFile(),
        id: '2',
        name: 'File 1',
        permission: FilePermission.Owner,
        is_folder: false,
        created_at: '2024-01-01',
      },
      {
        ...getMockFile(),
        id: '3',
        name: 'File 2',
        permission: FilePermission.Owner,
        is_folder: false,
        created_at: '2024-01-02',
      },
    ],
  },
  { ...getMockFile(), id: '4', name: 'File 3', is_folder: false, created_at: '2024-01-01' },
  { ...getMockFile(), id: '5', name: 'File 4', is_folder: false, created_at: '2024-01-02' },
];

const mockProps = {
  files: cloneDeep(mockFiles),
  activeFiles: [],
  folderExpanded: { '1': true },
  currentFileId: null,
  newFileId: '',
  setNewFileId: vi.fn(),
  isFetching: false,
  onChangeFile: vi.fn(),
  onSelectFile: vi.fn(),
  onRefresh: vi.fn(),
  onCreateFile: vi.fn(),
  onToggleExpand: vi.fn(),
  onDeleteFile: vi.fn(),
  onMoveFile: vi.fn(),
};

describe('FileList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders file list correctly', async () => {
    render(<FileList {...mockProps} />);

    expect(await screen.findByText('Folder 1')).toBeInTheDocument();
    expect(await screen.findByText('File 1')).toBeInTheDocument();
    expect(await screen.findByText('File 2')).toBeInTheDocument();
  });

  test('handles file selection', async () => {
    render(<FileList {...mockProps} />);

    const fileItem = await screen.findByText(mockFiles[1].name);
    fireEvent.click(fileItem);
    expect(mockProps.onSelectFile).toHaveBeenCalledWith(mockFiles[1]);
  });

  test('shows share drawer when triggered', async () => {
    render(<FileList {...mockProps} />);

    const actionsButton = await screen.findAllByTitle('actions');
    fireEvent.click(actionsButton[1]);
    const shareButton = await screen.findByText('Share');
    fireEvent.click(shareButton);

    expect(await screen.findByText('File Sharing')).toBeInTheDocument();
  });

  test('folder expand and collapse', async () => {
    render(<FileList {...mockProps} />);

    const folderItem = await screen.findByText('Folder 1');
    fireEvent.click(folderItem);

    expect(mockProps.onToggleExpand).toHaveBeenCalledWith('1');
  });

  test('change file name', async () => {
    render(<FileList {...mockProps} />);

    const fileItem = (await screen.findByText('File 1')).closest('[role="treeitem"]') as HTMLElement;
    fireEvent.mouseEnter(fileItem);
    // eslint-disable-next-line
    const actionsButton = await findByTitle(fileItem, 'actions');
    fireEvent.click(actionsButton);
    const renameButton = await screen.findByText('Rename');
    fireEvent.click(renameButton);

    const input = fileItem.querySelector('input')!;
    fireEvent.change(input, { target: { value: 'New File Name' } });
    fireEvent.keyDown(input, { key: 'Enter' });

    // wait for the request to complete
    await waitFor(() => {
      expect(mockProps.onChangeFile).toHaveBeenCalledWith(FileChangeType.UPDATE, {
        id: '2',
        name: 'New File Name',
      });
    });
  });

  test('delete file', async () => {
    render(<FileList {...mockProps} />);

    const fileItem = (await screen.findByText('File 1')).closest('[role="treeitem"]') as HTMLElement;
    fireEvent.mouseEnter(fileItem);
    // eslint-disable-next-line
    const actionsButton = await findByTitle(fileItem, 'actions');
    fireEvent.click(actionsButton);
    const deleteButton = await screen.findByText('Delete');
    fireEvent.click(deleteButton);
    // confirm the deletion
    const confirmButton = await screen.findByText('Confirm');
    fireEvent.click(confirmButton);

    // wait for the request to complete
    await waitFor(() => {
      expect(mockProps.onChangeFile).toHaveBeenCalledWith(FileChangeType.DELETE, mockFiles[0].files[0]);
    });
  });
});
