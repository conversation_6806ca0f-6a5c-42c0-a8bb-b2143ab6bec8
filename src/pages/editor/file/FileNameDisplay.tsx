import { KeyboardEvent, useEffect, useState } from 'react';
import { Input } from '@tigergraph/app-ui-lib/input';
import { expand } from 'inline-style-expand-shorthand';
import { FileStore } from '@/utils/graphEditor/data';

interface FileNameDisplayProps {
  file: FileStore;
  isEditing: boolean;
  onRename: (newName: string) => void;
  onCancelEdit: () => void;
  unsaved: boolean;
}

export function FileNameDisplay({ file, isEditing, onRename, onCancelEdit, unsaved }: FileNameDisplayProps) {
  const [tempName, setTempName] = useState<string>('');

  useEffect(() => {
    setTempName(file.name);
  }, [file.name, isEditing]);

  useEffect(() => {
    if (isEditing) {
      setTimeout(() => {
        const input = document.querySelector(`input[name="file-name-input-${file.id}"]`) as HTMLInputElement;
        input?.focus();
        input.select();
      }, 200);
    }
  }, [file.id, isEditing]);

  const handleRename = () => {
    if (tempName === file.name) {
      onCancelEdit();
      return;
    }
    onRename(tempName);
  };

  if (isEditing) {
    return (
      <Input
        value={tempName}
        name={`file-name-input-${file.id}`}
        autoFocus
        onChange={(e) => {
          const { currentTarget } = e;
          setTempName(() => currentTarget.value);
        }}
        onKeyDown={(e: KeyboardEvent) => {
          switch (e.key) {
            case 'Enter':
              handleRename();
              return;
            case 'Escape':
              onCancelEdit();
              return;
          }
        }}
        onBlur={handleRename}
        overrides={{
          Root: {
            style: {
              ...expand({ borderWidth: '1px' }),
              height: '24px',
            },
          },
          Input: {
            style: {
              fontSize: '12px',
              ...expand({ padding: '0 0 0 4px' }),
            },
          },
        }}
      />
    );
  }

  return (
    <div className="flex items-center w-full overflow-hidden text-ellipsis whitespace-nowrap leading-[16px]">
      <span className="relative overflow-hidden text-ellipsis whitespace-nowrap">{file.name}</span>
      {unsaved && <span>*</span>}
    </div>
  );
}
