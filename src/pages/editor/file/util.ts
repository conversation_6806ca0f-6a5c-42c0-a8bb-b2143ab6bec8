import { ActiveFile, FilePermission, FileStore } from '@/utils/graphEditor/data';
import { nanoid } from 'nanoid';

export const getPlaceholderFile = (): FileStore => {
  return {
    // hardcode id, so that we can customize styles through css selector li[data-nodeid='file-loading']
    id: 'file-loading',
    name: '',
    files: [],
    content: '',
    parent_id: '',
    is_folder: false,
    type: 'Placeholder',
    created_at: new Date(0).toISOString(),
    updated_at: new Date(0).toISOString(),
    permission: FilePermission.Edit,
  };
};

// temp file is created from a query
export const getTempFile = (name: string, content: string, query_name: string, graph_name: string): ActiveFile => {
  return {
    id: nanoid(),
    name,
    files: [],
    content,
    parent_id: '',
    is_folder: false,
    is_temp: true,
    type: 'UserFile',
    created_at: new Date(0).toISOString(),
    updated_at: new Date(0).toISOString(),
    permission: FilePermission.Owner,
    query_name,
    graphName: graph_name,
  };
};

export const getSharedFolder = (orgId: string): FileStore => {
  return {
    files: [],
    is_folder: true,
    id: `${orgId}-shared-folder`,
    name: 'Shared Folder',
    org_id: orgId,
    created_at: new Date(0).toISOString(),
    updated_at: new Date(0).toISOString(),
    permission: FilePermission.View,
    content: '',
    parent_id: '',
    type: 'UserFile',
    isShared: true,
  };
};

export const cypherTutorialFolderId = 'cypher-tutorial-folder';
export const getCypherTutorialFolder = (): FileStore => {
  return {
    is_folder: true,
    id: cypherTutorialFolderId,
    name: 'Cypher Tutorials',
    permission: FilePermission.View,
    content: '',
    parent_id: '',
    type: 'Tutorial',
    created_at: new Date(0).toISOString(),
    updated_at: new Date(0).toISOString(),
    files: [getPlaceholderFile()],
  };
};

export const ldbcTutorialFolderId = 'ldbc-tutorial-folder';
export const financialTutorialFolderId = 'financial-tutorial-folder';
export const getTutorialFolder = (id: string, name: string, parentId: string, tutorialType: string): FileStore => {
  return {
    is_folder: true,
    id,
    name,
    permission: FilePermission.View,
    content: '',
    parent_id: parentId,
    type: 'Tutorial',
    created_at: new Date(0).toISOString(),
    updated_at: new Date(0).toISOString(),
    files: [getPlaceholderFile()],
    tutorialType,
  };
};

export const gsqlTutorialFolderId = 'gsql-tutorial-folder';
export const getGSQLTutorialFolder = (): FileStore => {
  return {
    is_folder: true,
    id: gsqlTutorialFolderId,
    name: 'GSQL Tutorials',
    permission: FilePermission.View,
    content: '',
    parent_id: '',
    type: 'Tutorial',
    created_at: new Date(0).toISOString(),
    updated_at: new Date(0).toISOString(),
    files: [
      getTutorialFolder(financialTutorialFolderId, 'Financial', gsqlTutorialFolderId, 'GSQL'),
      getTutorialFolder(ldbcTutorialFolderId, 'LDBC', gsqlTutorialFolderId, 'LDBC'),
    ],
    tutorialType: 'GSQL',
  };
};

export const getTutorialFile = (id: string, name: string, parent: string, type: string): FileStore => {
  return {
    id,
    name,
    is_folder: false,
    permission: FilePermission.View,
    content: '',
    parent_id: parent,
    type: 'Tutorial',
    created_at: new Date(0).toISOString(),
    updated_at: new Date(0).toISOString(),
    files: [],
    tutorialType: type,
  };
};

export const appendUseGraph = (query: string, graphName: string): string => {
  const useGraph = `USE GRAPH ${graphName}\n`;
  return useGraph + query;
};

export const insertOrReplaceToQuery = (query: string): string => {
  const createQueryRegex = /^(CREATE\s+)(OR\s+REPLACE\s+)?(DISTRIBUTED\s+)?(QUERY\s+\w+)/i;
  const match = query.match(createQueryRegex);

  if (match && !match[2]) {
    return query.replace(/^(CREATE\s+)/i, '$1OR REPLACE ');
  }

  return query;
};
