import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ActiveFile, FileChangeType, FileStore } from '@/utils/graphEditor/data';
import React, { useCallback, useState } from 'react';

import { FileItem } from '@/pages/editor/file/FileItem';
import { ShareDrawer } from '@/pages/editor/file/ShareDrawer';
import { BaseFileList } from '@/pages/editor/file/BaseFileList';

interface FileListProps {
  files: FileStore[];
  activeFiles: ActiveFile[];
  folderExpanded: Record<string, boolean>;
  currentFileId: string | null;
  newFileId: string;
  setNewFileId: React.Dispatch<React.SetStateAction<string>>;
  isFetching: boolean;
  onChangeFile: (type: FileChangeType, payload: FileStore) => void;
  onSelectFile: (file: FileStore) => void;
  onRefresh: () => void;
  onCreateFile: (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;
  onToggleExpand: (id: string) => void;
  onDeleteFile: (file: FileStore) => void;
  onMoveFile: (fromId: string, parentFileId: string | null) => void;
}

const preventDefault = (e: Event) => e.preventDefault();

export function FileList({
  files,
  activeFiles,
  folderExpanded,
  currentFileId,
  newFileId,
  setNewFileId,
  isFetching,
  onChangeFile,
  onSelectFile,
  onRefresh,
  onCreateFile,
  onToggleExpand,
  onDeleteFile,
  onMoveFile,
}: FileListProps) {
  const [css, theme] = useStyletron();

  const [showShareDrawer, setShowShareDrawer] = useState<boolean>(false);
  const [shareFile, setShareFile] = useState<FileStore | null>(null);

  const renderTreeNode = useCallback(
    (file: FileStore, folder: FileStore | null, { onFileMenuOpen }: { onFileMenuOpen: (isOpen: boolean) => void }) => {
      return (
        <FileItem
          key={file.id}
          file={file}
          parent={folder}
          allFiles={files}
          newFileId={newFileId}
          setNewFileId={setNewFileId}
          onSelectFile={onSelectFile}
          onChangeFile={onChangeFile}
          onCreateFile={onCreateFile}
          onToggleExpand={onToggleExpand}
          onOpenShareDrawer={(file) => {
            setShowShareDrawer(true);
            setShareFile(file);
          }}
          onFileMenuOpen={onFileMenuOpen}
          onDeleteFile={onDeleteFile}
          onMoveFile={onMoveFile}
        />
      );
    },
    [files, newFileId, onChangeFile, onCreateFile, onSelectFile, onToggleExpand, setNewFileId, onDeleteFile, onMoveFile]
  );

  return (
    <>
      <BaseFileList
        files={files}
        activeFiles={activeFiles}
        folderExpanded={folderExpanded}
        currentFileId={currentFileId}
        isFetching={isFetching}
        type="file"
        renderTreeNode={renderTreeNode}
        onCreateFile={onCreateFile}
        onRefresh={onRefresh}
        onToggleExpand={onToggleExpand}
      />
      {showShareDrawer && (
        <ShareDrawer visible={showShareDrawer} file={shareFile!} onClose={() => setShowShareDrawer(false)} />
      )}
    </>
  );
}
