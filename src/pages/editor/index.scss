.download-btn {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.download-btn:hover {
  border-radius: 28px;
  box-shadow: 0 3px 5px 3px rgba(170, 181, 191, 0.3);
  fill-opacity: 1;
}

.download-btn:hover path:first-child {
  fill-opacity: 1;
}

.download-btn.active {
  border-radius: 28px;
  box-shadow: 0px 4px 6px 3px rgba(170, 181, 191, 0.3);
}

.download-btn.active path:first-child {
  fill-opacity: 1;
}

/* .cm-completionIcon-keyword::after {
  content: url('/src/assets/symbol-keyword.svg') !important;
}

.cm-completionIcon.cm-completionIcon-keyword {
  vertical-align: middle;
  margin-right: 0.3rem;
}

.cm-completionIcon-function::after {
  content: url('/src/assets/symbol-function.svg') !important;
}

.cm-completionIcon.cm-completionIcon-function {
  vertical-align: middle;
  margin-right: 0.3rem;
}

.cm-completionIcon-operator::after {
  content: url('/src/assets/symbol-operator.svg') !important;
}

.cm-completionIcon.cm-completionIcon-operator {
  vertical-align: middle;
  margin-right: 0.3rem;
}

.cm-completionIcon-type::after {
  content: url('/src/assets/symbol-type.svg') !important;
}

.cm-completionIcon.cm-completionIcon-type {
  vertical-align: middle;
  margin-right: 0.3rem;
}

.cm-completionIcon-word::after {
  content: url('/src/assets/symbol-word.svg') !important;
}

.cm-completionIcon.cm-completionIcon-word {
  vertical-align: middle;
  margin-right: 0.3rem;
}

.cm-completionIcon-multi_keyword::after {
  content: url('/src/assets/symbol-multiwords.svg') !important;
}

.cm-completionIcon.cm-completionIcon-multi_keyword {
  vertical-align: middle;
  margin-right: 0.3rem;
} */

.close-icon {
  visibility: hidden;
}
button[data-baseweb='tab']:hover .close-icon {
  visibility: visible;
}

div[data-baseweb='tree-node-label'] {
  .file-actions {
    width: 0;
    visibility: hidden;
  }
}

div[data-baseweb='tree-node-label']:hover {
  .file-actions {
    width: auto;
    visibility: visible;
  }
}

div:has(> div[data-baseweb='tree-node-label']) {
  width: calc(100% - 22px);
}

input::placeholder {
  font-size: 14px;
}

/* customize styles for file loading skeleton */
li[data-nodeid='file-loading'] > div {
  background-color: transparent !important;
}
li[data-nodeid='file-loading'] > div:hover {
  background-color: transparent !important;
}

// Code lint styles.
.cm-tooltip.cm-tooltip-lint {
  border: none;
  background: transparent;
}
.cm-diagnostic {
  white-space: pre-wrap;
  padding: 0;
  color: var(--text-primary);
  background-color: var(--background-primary);

  &.cm-diagnostic-error {
    border: 1px solid var(--border-tertiary);
    border-left: 3px solid #bf360c;
  }

  &.cm-diagnostic-warning {
    border: 1px solid var(--border-tertiary);
    border-left: 3px solid #ffee58;
  }
}

.cm-gutter.cm-gutter-lint {
  width: 5px;

  .cm-lint-marker {
    width: 2px;
    height: 21px;
    display: inline-block;
    cursor: pointer;
  }
}

.cm-lint-marker-error,
.cm-lint-marker-warning {
  padding-right: 2px;
}
